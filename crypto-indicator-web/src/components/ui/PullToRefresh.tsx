import React, { useCallback, useRef, useState } from 'react';

// Component-specific styles
import '../../styles/components/pull-to-refresh.css';

interface PullToRefreshProps {
  onRefresh: () => Promise<void>;
  children: React.ReactNode;
  isLoading?: boolean;
  disabled?: boolean;
}

const PULL_THRESHOLD = 80; // Distance to trigger refresh
const MAX_PULL_DISTANCE = 120; // Maximum pull distance
const RESISTANCE_FACTOR = 0.5; // Resistance when pulling
const REFRESH_INDICATOR_OFFSET = 60; // Offset for refresh indicator

// eslint-disable-next-line max-lines-per-function
export const PullToRefresh: React.FC<PullToRefreshProps> = ({
  onRefresh,
  children,
  isLoading = false,
  disabled = false,
}) => {
  const [isPulling, setIsPulling] = useState(false);
  const [pullDistance, setPullDistance] = useState(0);
  const [isRefreshing, setIsRefreshing] = useState(false);
  
  const startY = useRef<number>(0);
  const currentY = useRef<number>(0);
  const containerRef = useRef<HTMLDivElement>(null);

  const handleTouchStart = useCallback((e: React.TouchEvent) => {
    if (disabled || isLoading || isRefreshing) {return;}

    // Only start pull-to-refresh if we're at the top of the container
    const container = containerRef.current;
    if (!container || container.scrollTop > 0) {return;}

    const [touch] = e.touches;
    if (!touch) {return;}

    startY.current = touch.clientY;
    setIsPulling(true);
  }, [disabled, isLoading, isRefreshing]);

  const handleTouchMove = useCallback((e: React.TouchEvent) => {
    if (!isPulling || disabled || isLoading || isRefreshing) {return;}

    const [touch] = e.touches;
    if (!touch) {return;}

    currentY.current = touch.clientY;
    const deltaY = currentY.current - startY.current;

    // Only allow pulling down
    if (deltaY > 0) {
      // Apply resistance to make pulling feel natural
      const resistedDistance = Math.min(
        deltaY * RESISTANCE_FACTOR,
        MAX_PULL_DISTANCE
      );
      setPullDistance(resistedDistance);
      
      // Prevent default scrolling when pulling
      e.preventDefault();
    }
  }, [isPulling, disabled, isLoading, isRefreshing]);

  const handleTouchEnd = useCallback(async () => {
    if (!isPulling || disabled || isLoading || isRefreshing) {return;}

    setIsPulling(false);

    // Trigger refresh if pulled far enough
    if (pullDistance >= PULL_THRESHOLD) {
      setIsRefreshing(true);
      try {
        await onRefresh();
      } catch (error) {
        // eslint-disable-next-line no-console
        console.error('Pull-to-refresh failed:', error);
      } finally {
        setIsRefreshing(false);
      }
    }

    // Reset pull distance
    setPullDistance(0);
  }, [isPulling, pullDistance, onRefresh, disabled, isLoading, isRefreshing]);

  const shouldShowRefreshIndicator = isPulling || isRefreshing;
  const refreshProgress = Math.min(pullDistance / PULL_THRESHOLD, 1);
  const isReadyToRefresh = pullDistance >= PULL_THRESHOLD;

  return (
    <div
      ref={containerRef}
      className="pull-to-refresh-container"
      onTouchStart={handleTouchStart}
      onTouchMove={handleTouchMove}
      onTouchEnd={handleTouchEnd}
      style={{
        transform: isPulling ? `translateY(${pullDistance}px)` : 'translateY(0)',
        transition: isPulling ? 'none' : 'transform 0.3s ease-out',
      }}
    >
      {/* Refresh Indicator */}
      {shouldShowRefreshIndicator && (
        <div 
          className={`refresh-indicator ${isReadyToRefresh ? 'ready' : ''} ${isRefreshing ? 'refreshing' : ''}`}
          style={{
            opacity: Math.max(refreshProgress, isRefreshing ? 1 : 0),
            transform: `translateY(${pullDistance - 60}px)`,
          }}
        >
          <div className="refresh-spinner">
            <div className="spinner-ring" />
            <div className="refresh-icon">
              {isRefreshing ? '⟳' : (isReadyToRefresh ? '↓' : '↓')}
            </div>
          </div>
          <div className="refresh-text">
            {isRefreshing 
              ? 'Refreshing...' 
              : (isReadyToRefresh 
                ? 'Release to refresh' 
                : 'Pull to refresh')
            }
          </div>
        </div>
      )}

      {/* Content */}
      <div className="pull-to-refresh-content">
        {children}
      </div>
    </div>
  );
};
