import React, { useState } from 'react';

import { useResponsiveLayout } from '@/hooks/useResponsiveLayout';

import { DesktopLayout } from './DesktopLayout';
import { MobileLayout } from './MobileLayout';
import { TabletLayout } from './TabletLayout';

import type {
  CryptoCurrencyStatisticsDto,
  IndicatorValueDto,
  StockStatisticsDto,
} from '@/generated';
import type {
  FilterConfig,
  SortColumn,
  SortDirection,
  StockFilterConfig,
  StockSortColumn,
} from '@/types/table';

// Component-specific styles
import '../../styles/layout/responsive-layout.css';

interface ResponsiveTableContainerProps {
  data: CryptoCurrencyStatisticsDto[] | StockStatisticsDto[];
  btcStatistics: CryptoCurrencyStatisticsDto[] | StockStatisticsDto[];
  onSignalClick: (symbol: string, currency: string) => void;
  formatDate: (date?: string) => string;
  findBtcDataForSymbol: (
    btcStats: CryptoCurrencyStatisticsDto[] | StockStatisticsDto[],
    symbol: string,
  ) => IndicatorValueDto | undefined;

  // Sorting props
  onSort: (column: SortColumn | StockSortColumn) => void;
  getSortDirection: (column: SortColumn | StockSortColumn) => SortDirection;

  // Filtering props
  filterConfig: FilterConfig | StockFilterConfig;
  onSymbolSearchChange: (search: string) => void;
  onUsdSignalChange: (
    signal: FilterConfig['usdSignal'] | StockFilterConfig['usdSignal'],
  ) => void;
  onBtcSignalChange: (signal: FilterConfig['btcSignal']) => void;
  onClearFilters: () => void;
  hasActiveFilters: boolean;
  filteredCount: number;
  totalCount: number;
  assetType?: 'crypto' | 'stock';

  // Pull-to-refresh props
  onRefresh?: () => Promise<void>;
  isLoading?: boolean;
}

export const ResponsiveTableContainer: React.FC<
  ResponsiveTableContainerProps
  // eslint-disable-next-line max-lines-per-function
> = ({
  data,
  btcStatistics,
  onSignalClick,
  formatDate,
  findBtcDataForSymbol,
  onSort,
  getSortDirection,
  filterConfig,
  onSymbolSearchChange,
  onUsdSignalChange,
  onBtcSignalChange,
  onClearFilters,
  hasActiveFilters,
  filteredCount,
  totalCount,
  assetType = 'crypto',
  onRefresh,
  isLoading = false,
}) => {
  const { layoutType, isMobile, isTablet } = useResponsiveLayout();
  const [isFilterDrawerOpen, setIsFilterDrawerOpen] = useState(false);

  return (
    <div className={`responsive-table-container layout-${layoutType}`}>
      {isMobile && (
        <MobileLayout
          data={data}
          btcStatistics={btcStatistics}
          onSignalClick={onSignalClick}
          formatDate={formatDate}
          findBtcDataForSymbol={findBtcDataForSymbol}
          filterConfig={filterConfig}
          onUsdSignalChange={onUsdSignalChange}
          onBtcSignalChange={onBtcSignalChange}
          onClearFilters={onClearFilters}
          hasActiveFilters={hasActiveFilters}
          filteredCount={filteredCount}
          totalCount={totalCount}
          isFilterDrawerOpen={isFilterDrawerOpen}
          onFilterDrawerToggle={setIsFilterDrawerOpen}
          assetType={assetType}
          {...(onRefresh && { onRefresh })}
          isLoading={isLoading}
        />
      )}
      {isTablet && (
        <TabletLayout
          data={data}
          btcStatistics={btcStatistics}
          onSignalClick={onSignalClick}
          formatDate={formatDate}
          findBtcDataForSymbol={findBtcDataForSymbol}
          filterConfig={filterConfig}
          onSymbolSearchChange={onSymbolSearchChange}
          onUsdSignalChange={onUsdSignalChange}
          onBtcSignalChange={onBtcSignalChange}
          onClearFilters={onClearFilters}
          hasActiveFilters={hasActiveFilters}
          filteredCount={filteredCount}
          totalCount={totalCount}
          assetType={assetType}
        />
      )}
      {layoutType === 'desktop' && (
        <DesktopLayout
          data={data}
          btcStatistics={btcStatistics}
          onSignalClick={onSignalClick}
          formatDate={formatDate}
          findBtcDataForSymbol={findBtcDataForSymbol}
          onSort={onSort}
          getSortDirection={getSortDirection}
          filterConfig={filterConfig}
          onSymbolSearchChange={onSymbolSearchChange}
          onUsdSignalChange={onUsdSignalChange}
          onBtcSignalChange={onBtcSignalChange}
          onClearFilters={onClearFilters}
          hasActiveFilters={hasActiveFilters}
          filteredCount={filteredCount}
          totalCount={totalCount}
          assetType={assetType}
        />
      )}
    </div>
  );
};
