/* eslint-disable max-lines */
import React, { useEffect, useMemo, useState } from 'react';

import { AppStateHandler } from '@/components/AppStateHandler';
import { ChartModalStates } from '@/components/chart/ChartModalStates';
import { StockMainContent } from '@/components/stock/StockMainContent';
import { DashboardHeader } from '@/components/ui/DashboardHeader';
import { TabNavigation } from '@/components/ui/TabNavigation';
import { CSS_CLASSES, TIMING } from '@/constants/app';
import { useChartData } from '@/hooks/useChartData';
import { useCryptoData } from '@/hooks/useCryptoData';
import { useFiltering } from '@/hooks/useFiltering';
import { useSorting } from '@/hooks/useSorting';
import { useStockChartData } from '@/hooks/useStockChartData';
import { useStockData } from '@/hooks/useStockData';
import { useStockFiltering } from '@/hooks/useStockFiltering';
import { useStockSorting } from '@/hooks/useStockSorting';
import { useTabNavigation } from '@/hooks/useTabNavigation';
import { DEFAULT_TABS } from '@/types/tabs';
import {
  findBtcDataForSymbol,
  formatDate,
  processCryptoStatistics,
} from '@/utils/dataProcessors';
import { processStockStatistics } from '@/utils/stockDataProcessors';
import { applyStockFilters } from '@/utils/stockTableFiltering';
import { applyStockSorting } from '@/utils/stockTableSorting';
import { applyFilters } from '@/utils/tableFiltering';
import { applySorting } from '@/utils/tableSorting';

import { MainContent } from './MainContent';

// Component-specific styles
import '../../styles/components/table.css';

// eslint-disable-next-line max-lines-per-function, max-statements
const StatisticsTable: React.FC = () => {
  const [showChart, setShowChart] = useState(false);

  // Tab navigation
  const { activeTab, setActiveTab } = useTabNavigation('crypto');

  // Crypto data and hooks
  const {
    data: cryptoStatistics,
    loading: cryptoLoading,
    error: cryptoError,
    fetchData: fetchCryptoData,
  } = useCryptoData();
  const {
    chartData: cryptoChartData,
    chartLoading: cryptoChartLoading,
    chartError: cryptoChartError,
    fetchChartData: fetchCryptoChartData,
    clearChartData: clearCryptoChartData,
  } = useChartData();
  const {
    sortConfig: cryptoSortConfig,
    handleSort: handleCryptoSort,
    getSortDirection: getCryptoSortDirection,
  } = useSorting();
  const {
    filterConfig: cryptoFilterConfig,
    updateSymbolSearch: updateCryptoSymbolSearch,
    updateUsdSignal,
    updateBtcSignal,
    clearFilters: clearCryptoFilters,
    hasActiveFilters: hasCryptoActiveFilters,
  } = useFiltering();

  // Stock data and hooks
  const {
    data: stockStatistics,
    loading: stockLoading,
    error: stockError,
    fetchData: fetchStockData,
  } = useStockData();
  const {
    chartData: stockChartData,
    chartLoading: stockChartLoading,
    chartError: stockChartError,
    fetchChartData: fetchStockChartData,
    clearChartData: clearStockChartData,
  } = useStockChartData();
  const {
    sortConfig: stockSortConfig,
    handleSort: handleStockSort,
    getSortDirection: getStockSortDirection,
  } = useStockSorting();
  const {
    filterConfig: stockFilterConfig,
    updateSymbolSearch: updateStockSymbolSearch,
    updateUsdSignal: updateStockUsdSignal,
    updateBtcSignal: updateStockBtcSignal,
    clearFilters: clearStockFilters,
    hasActiveFilters: hasStockActiveFilters,
  } = useStockFiltering();

  // Process crypto data with memoization for performance
  const {
    usdStatistics,
    btcStatistics,
    totalCount: cryptoTotalCount,
  } = useMemo(
    () => processCryptoStatistics(cryptoStatistics),
    [cryptoStatistics],
  );

  // Process stock data with memoization for performance
  const {
    stockStatistics: processedStockStatistics,
    totalCount: stockTotalCount,
    btcStatistics: stockBtcStatistics,
  } = useMemo(() => processStockStatistics(stockStatistics), [stockStatistics]);

  // Apply filtering and sorting for crypto data
  const processedCryptoData = useMemo(() => {
    const filtered = applyFilters(
      usdStatistics,
      btcStatistics,
      cryptoFilterConfig,
      findBtcDataForSymbol,
    );
    return applySorting(
      filtered,
      btcStatistics,
      cryptoSortConfig,
      findBtcDataForSymbol,
    );
  }, [usdStatistics, btcStatistics, cryptoFilterConfig, cryptoSortConfig]);

  // Apply filtering and sorting for stock data
  const processedStockData = useMemo(() => {
    const filtered = applyStockFilters(
      processedStockStatistics,
      stockBtcStatistics,
      stockFilterConfig,
      findBtcDataForSymbol,
    );
    return applyStockSorting(
      filtered,
      stockBtcStatistics,
      stockSortConfig,
      findBtcDataForSymbol,
    );
  }, [processedStockStatistics, stockBtcStatistics, stockFilterConfig, stockSortConfig]);

  const cryptoFilteredCount = processedCryptoData.length;
  const stockFilteredCount = processedStockData.length;

  const handleCryptoSignalClick = async (
    symbol: string,
    conversionCurrency: string,
  ) => {
    await fetchCryptoChartData(symbol, conversionCurrency);
    setShowChart(true);
  };

  const handleStockSignalClick = async (
    symbol: string,
    conversionCurrency: string,
  ) => {
    await fetchStockChartData(symbol, conversionCurrency);
    setShowChart(true);
  };

  const closeChart = () => {
    setShowChart(false);
    clearCryptoChartData();
    clearStockChartData();
  };

  useEffect(() => {
    const handleFetchData = () => {
      fetchCryptoData().catch(error => {
        // eslint-disable-next-line no-console
        console.error('Failed to fetch crypto data:', error);
      });
      fetchStockData().catch(error => {
        // eslint-disable-next-line no-console
        console.error('Failed to fetch stock data:', error);
      });
    };

    handleFetchData();
    const interval = setInterval(handleFetchData, TIMING.DATA_REFRESH_INTERVAL);
    return () => {
      clearInterval(interval);
    };
  }, [fetchCryptoData, fetchStockData]);

  return (
    <AppStateHandler
      loading={activeTab === 'crypto' ? cryptoLoading : stockLoading}
      error={activeTab === 'crypto' ? cryptoError : stockError}
      statisticsLength={
        activeTab === 'crypto'
          ? cryptoStatistics.length
          : stockStatistics.length
      }
      onRetry={() => {
        if (activeTab === 'crypto') {
          fetchCryptoData().catch(error => {
            // eslint-disable-next-line no-console
            console.error('Failed to retry fetch crypto data:', error);
          });
        } else {
          fetchStockData().catch(error => {
            // eslint-disable-next-line no-console
            console.error('Failed to retry fetch stock data:', error);
          });
        }
      }}
    >
      <div className={CSS_CLASSES.APP_CONTAINER}>
        <DashboardHeader />

        {/* Tab Navigation */}
        <TabNavigation
          activeTab={activeTab}
          onTabChange={setActiveTab}
          tabs={DEFAULT_TABS}
        />

        {/* Conditional Content Based on Active Tab */}
        {activeTab === 'crypto' ? (
          <MainContent
            processedData={processedCryptoData}
            btcStatistics={btcStatistics}
            totalCount={cryptoTotalCount}
            filteredCount={cryptoFilteredCount}
            loading={cryptoLoading}
            filterConfig={cryptoFilterConfig}
            hasActiveFilters={hasCryptoActiveFilters}
            onSignalClick={handleCryptoSignalClick}
            onRefresh={async () => {
              await fetchCryptoData();
            }}
            onSort={handleCryptoSort}
            getSortDirection={getCryptoSortDirection}
            onSymbolSearchChange={updateCryptoSymbolSearch}
            onUsdSignalChange={updateUsdSignal}
            onBtcSignalChange={updateBtcSignal}
            onClearFilters={clearCryptoFilters}
            formatDate={formatDate}
            findBtcDataForSymbol={findBtcDataForSymbol}
          />
        ) : (
          <StockMainContent
            data={processedStockData}
            totalCount={stockTotalCount}
            filteredCount={stockFilteredCount}
            loading={stockLoading}
            filterConfig={stockFilterConfig}
            hasActiveFilters={hasStockActiveFilters}
            onSignalClick={handleStockSignalClick}
            onRefresh={() => {
              fetchStockData().catch(error => {
                // eslint-disable-next-line no-console
                console.error('Failed to refresh stock data:', error);
              });
            }}
            onSort={handleStockSort}
            getSortDirection={getStockSortDirection}
            onSymbolSearchChange={updateStockSymbolSearch}
            onUsdSignalChange={updateStockUsdSignal}
            onBtcSignalChange={updateStockBtcSignal}
            onClearFilters={clearStockFilters}
            btcStatistics={stockBtcStatistics}
            findBtcDataForSymbol={findBtcDataForSymbol}
          />
        )}

        <ChartModalStates
          showChart={showChart}
          chartData={activeTab === 'crypto' ? cryptoChartData : stockChartData}
          chartLoading={
            activeTab === 'crypto' ? cryptoChartLoading : stockChartLoading
          }
          chartError={
            activeTab === 'crypto' ? cryptoChartError : stockChartError
          }
          onClose={closeChart}
        />
      </div>
    </AppStateHandler>
  );
};

export default StatisticsTable;
