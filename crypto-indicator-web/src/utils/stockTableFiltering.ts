import { matchesSignalFilter, matchesSymbolSearch } from './tableFiltering';

import type { IndicatorValueDto, StockStatisticsDto } from "@/generated";
import type { StockFilterConfig } from "@/types/table";

/**
 * Apply filters to stock data
 */
export const applyStockFilters = (
  data: StockStatisticsDto[],
  btcStatistics: StockStatisticsDto[],
  filterConfig: StockFilterConfig,
  findBtcDataForSymbol: (btcStats: StockStatisticsDto[], symbol: string) => IndicatorValueDto | undefined
): StockStatisticsDto[] => {
  return data.filter((stock) => {
    const usdData = stock.indicatorValues?.[0];
    const btcData = findBtcDataForSymbol(btcStatistics, stock.symbol);

    // Symbol search filter
    if (!matchesSymbolSearch(stock.symbol, filterConfig.symbolSearch)) {
      return false;
    }

    // USD signal filter
    if (!matchesSignalFilter(usdData?.color, filterConfig.usdSignal)) {
      return false;
    }

    // BTC signal filter
    return matchesSignalFilter(btcData?.color, filterConfig.btcSignal);
  });
};
