import { useCallback, useMemo, useState } from "react";

import type { SignalColor, StockFilterConfig } from "@/types/table";

interface UseStockFilteringReturn {
  filterConfig: StockFilterConfig;
  updateSymbolSearch: (search: string) => void;
  updateUsdSignal: (signal: SignalColor) => void;
  updateBtcSignal: (signal: SignalColor) => void;
  clearFilters: () => void;
  hasActiveFilters: boolean;
}

const DEFAULT_FILTER_CONFIG: StockFilterConfig = {
  symbolSearch: '',
  usdSignal: 'all',
  btcSignal: 'all',
};

export const useStockFiltering = (): UseStockFilteringReturn => {
  const [filterConfig, setFilterConfig] = useState<StockFilterConfig>(DEFAULT_FILTER_CONFIG);

  const updateSymbolSearch = useCallback((search: string): void => {
    setFilterConfig(prev => ({ ...prev, symbolSearch: search }));
  }, []);

  const updateUsdSignal = useCallback((signal: SignalColor): void => {
    setFilterConfig(prev => ({ ...prev, usdSignal: signal }));
  }, []);

  const updateBtcSignal = useCallback((signal: SignalColor): void => {
    setFilterConfig(prev => ({ ...prev, btcSignal: signal }));
  }, []);

  const clearFilters = useCallback((): void => {
    setFilterConfig(DEFAULT_FILTER_CONFIG);
  }, []);

  const hasActiveFilters = useMemo((): boolean => {
    return filterConfig.symbolSearch !== '' || filterConfig.usdSignal !== 'all' || filterConfig.btcSignal !== 'all';
  }, [filterConfig]);

  return {
    filterConfig,
    updateSymbolSearch,
    updateUsdSignal,
    updateBtcSignal,
    clearFilters,
    hasActiveFilters,
  };
};
