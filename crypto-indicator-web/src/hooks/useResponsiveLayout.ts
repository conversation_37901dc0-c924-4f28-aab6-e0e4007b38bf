import { useEffect,useState } from 'react';

import { RESPONSIVE_CONSTANTS } from '@/constants/responsive';

export type LayoutType = 'mobile' | 'tablet' | 'desktop';

interface ResponsiveConfig {
  mobile: number;
  tablet: number;
  desktop: number;
}

const DEFAULT_BREAKPOINTS: ResponsiveConfig = {
  mobile: 768,
  tablet: 1024,
  desktop: RESPONSIVE_CONSTANTS.DESKTOP_MIN_WIDTH,
};

export const useResponsiveLayout = (breakpoints: ResponsiveConfig = DEFAULT_BREAKPOINTS) => {
  // Initialize with proper layout type based on current window size
  const getInitialLayoutType = (): LayoutType => {
    if (typeof window === 'undefined') return 'desktop';
    const width = window.innerWidth;
    if (width < breakpoints.mobile) return 'mobile';
    if (width < breakpoints.tablet) return 'tablet';
    return 'desktop';
  };

  const [layoutType, setLayoutType] = useState<LayoutType>(getInitialLayoutType);
  const [windowSize, setWindowSize] = useState({
    width: typeof window === 'undefined' ? RESPONSIVE_CONSTANTS.DESKTOP_MIN_WIDTH : window.innerWidth,
    height: typeof window === 'undefined' ? RESPONSIVE_CONSTANTS.TABLET_MIN_WIDTH : window.innerHeight,
  });

  useEffect(() => {
    const handleResize = () => {
      const width = window.innerWidth;
      const height = window.innerHeight;
      
      setWindowSize({ width, height });
      
      if (width < breakpoints.mobile) {
        setLayoutType('mobile');
      } else if (width < breakpoints.tablet) {
        setLayoutType('tablet');
      } else {
        setLayoutType('desktop');
      }
    };

    // Set initial layout
    handleResize();

    // Add event listener
    window.addEventListener('resize', handleResize);
    
    // Cleanup
    return () => { window.removeEventListener('resize', handleResize); };
  }, [breakpoints]);

  const isMobile = layoutType === 'mobile';
  const isTablet = layoutType === 'tablet';
  const isDesktop = layoutType === 'desktop';
  const isMobileOrTablet = isMobile || isTablet;

  return {
    layoutType,
    windowSize,
    isMobile,
    isTablet,
    isDesktop,
    isMobileOrTablet,
    breakpoints,
  };
};
