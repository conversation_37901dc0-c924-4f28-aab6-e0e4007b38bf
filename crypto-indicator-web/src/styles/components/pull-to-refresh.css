/**
 * Pull-to-Refresh Component Styles
 * 
 * Styles for the PullToRefresh component including container,
 * refresh indicator, spinner, and animations.
 * 
 * Dependencies: variables.css, base.css
 */

.pull-to-refresh-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  will-change: transform;
}

.pull-to-refresh-content {
  position: relative;
  width: 100%;
  min-height: 100%;
}

/* Refresh Indicator */
.refresh-indicator {
  position: absolute;
  top: -60px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 16px;
  background: rgb(var(--bg-primary-rgb), 0.95);
  border: 1px solid var(--border-primary);
  border-radius: 16px;
  box-shadow: var(--glass-shadow);
  backdrop-filter: var(--glass-blur);
  z-index: 1000;
  transition: opacity 0.2s ease;
  will-change: transform, opacity;
}

.refresh-indicator.ready {
  background: rgb(var(--accent-teal-rgb), 0.1);
  border-color: var(--accent-teal);
  box-shadow: 0 4px 20px rgba(148, 226, 213, 0.3);
}

.refresh-indicator.refreshing {
  background: rgb(var(--accent-blue-rgb), 0.1);
  border-color: var(--accent-blue);
  box-shadow: 0 4px 20px rgba(137, 180, 250, 0.3);
}

/* Refresh Spinner */
.refresh-spinner {
  position: relative;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.spinner-ring {
  position: absolute;
  width: 32px;
  height: 32px;
  border: 2px solid transparent;
  border-top: 2px solid var(--text-secondary);
  border-radius: 50%;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.refresh-indicator.ready .spinner-ring {
  border-top-color: var(--accent-teal);
  opacity: 0.6;
}

.refresh-indicator.refreshing .spinner-ring {
  border-top-color: var(--accent-blue);
  opacity: 1;
  animation: spin 1s linear infinite;
}

.refresh-icon {
  font-size: 16px;
  color: var(--text-secondary);
  transition: all 0.2s ease;
  transform-origin: center;
}

.refresh-indicator.ready .refresh-icon {
  color: var(--accent-teal);
  transform: rotate(180deg);
}

.refresh-indicator.refreshing .refresh-icon {
  color: var(--accent-blue);
  opacity: 0;
}

/* Refresh Text */
.refresh-text {
  font-size: 12px;
  font-weight: 500;
  color: var(--text-secondary);
  text-align: center;
  white-space: nowrap;
  transition: color 0.2s ease;
}

.refresh-indicator.ready .refresh-text {
  color: var(--accent-teal);
}

.refresh-indicator.refreshing .refresh-text {
  color: var(--accent-blue);
}

/* Animations */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Mobile Optimizations */
@media (max-width: 480px) {
  .refresh-indicator {
    top: -50px;
    padding: 12px 16px;
    border-radius: 12px;
  }

  .refresh-spinner {
    width: 28px;
    height: 28px;
  }

  .spinner-ring {
    width: 28px;
    height: 28px;
  }

  .refresh-icon {
    font-size: 14px;
  }

  .refresh-text {
    font-size: 11px;
  }
}

/* Tablet Optimizations */
@media (max-width: 768px) {
  .refresh-indicator {
    top: -55px;
    padding: 14px 18px;
    border-radius: 14px;
  }

  .refresh-spinner {
    width: 30px;
    height: 30px;
  }

  .spinner-ring {
    width: 30px;
    height: 30px;
  }

  .refresh-icon {
    font-size: 15px;
  }

  .refresh-text {
    font-size: 11.5px;
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .pull-to-refresh-container,
  .refresh-indicator,
  .spinner-ring,
  .refresh-icon,
  .refresh-text {
    transition: none;
    animation: none;
  }

  .refresh-indicator.refreshing .spinner-ring {
    animation: none;
    opacity: 0.8;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .refresh-indicator {
    background: var(--bg-primary);
    border: 2px solid var(--text-primary);
  }

  .refresh-indicator.ready {
    border-color: var(--accent-teal);
  }

  .refresh-indicator.refreshing {
    border-color: var(--accent-blue);
  }
}
