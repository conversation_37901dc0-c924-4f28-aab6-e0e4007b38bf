/**
 * Responsive Layout System Styles
 *
 * Styles for the responsive layout system including Apple Liquid Glass
 * design system, layout containers, and responsive breakpoints.
 *
 * Dependencies: variables.css, base.css
 */

/* Apple Liquid Glass Design System */

/* Enhanced Glass Effects */
.glass-primary {
  background: rgb(var(--bg-primary-rgb), var(--glass-opacity-primary));
  border: var(--glass-border-primary);
  box-shadow: var(--glass-shadow-primary);
  backdrop-filter: var(--glass-blur-primary);
}

.glass-secondary {
  background: rgb(var(--bg-secondary-rgb), var(--glass-opacity-secondary));
  border: var(--glass-border-secondary);
  box-shadow: var(--glass-shadow-secondary);
  backdrop-filter: var(--glass-blur-secondary);
}

.glass-tertiary {
  background: rgb(var(--bg-tertiary-rgb), var(--glass-opacity-tertiary));
  border: var(--glass-border-tertiary);
  box-shadow: var(--glass-shadow-tertiary);
  backdrop-filter: var(--glass-blur-tertiary);
}

/* Responsive Layout Containers */
.responsive-table-container {
  width: 100%;
  transition: all var(--spring-duration) var(--spring-easing);
}

/* Tablet Layout Styles */
.tablet-layout {
  padding: 16px;
}

.crypto-cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
  margin-top: 16px;
}

.tablet-layout .crypto-card {
  min-height: 140px;
}

/* Desktop Layout Enhancements */
.desktop-layout .table-wrapper {
  margin-top: 16px;
  overflow: hidden;
  background: rgb(var(--bg-secondary-rgb), var(--glass-opacity-secondary));
  border: var(--glass-border-secondary);
  border-radius: 16px;
  box-shadow: var(--glass-shadow-secondary);
  backdrop-filter: var(--glass-blur-secondary);
}

.desktop-layout .table {
  background: transparent;
}

/* Enhanced Table Filters for Desktop */
.desktop-layout .table-filters {
  background: rgb(var(--bg-primary-rgb), var(--glass-opacity-primary));
  border: var(--glass-border-primary);
  border-radius: 16px;
  box-shadow: var(--glass-shadow-primary);
  backdrop-filter: var(--glass-blur-primary);
}

/* Responsive Breakpoint Adjustments */
@media (width <= 767px) {
  .layout-mobile .table-filters,
  .layout-mobile .sortable-header,
  .desktop-layout .table-filters,
  .desktop-layout .sortable-header {
    display: none !important;
  }
}

@media (width >= 768px) and (width <= 1023px) {
  .layout-tablet .filter-toggle-container {
    display: none;
  }

  .layout-tablet .table-filters {
    background: rgb(var(--bg-secondary-rgb), var(--glass-opacity-secondary));
    border: var(--glass-border-secondary);
    border-radius: 12px;
    box-shadow: var(--glass-shadow-secondary);
    backdrop-filter: var(--glass-blur-secondary);
  }
}

@media (width >= 1024px) {
  .layout-desktop .filter-toggle-container,
  .layout-desktop .crypto-cards-container,
  .layout-desktop .crypto-cards-grid {
    display: none;
  }
}

/* Additional safeguard: Hide desktop components on mobile regardless of layout class */
@media (width <= 767px) {
  .desktop-layout,
  .table-filters,
  .sortable-header {
    display: none !important;
  }
}
